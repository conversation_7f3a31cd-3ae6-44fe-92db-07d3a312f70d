#!/usr/bin/env python3
"""
KontextFlux API 客户端启动脚本
"""

import subprocess
import sys
import os


def install_requirements():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    return True


def run_streamlit():
    """运行 Streamlit 应用"""
    print("正在启动 Streamlit 应用...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "main.py"])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def main():
    """主函数"""
    print("🎨 KontextFlux API 客户端")
    print("=" * 50)
    
    # 检查是否需要安装依赖
    if not os.path.exists("requirements.txt"):
        print("❌ 未找到 requirements.txt 文件")
        return
    
    # 安装依赖
    if not install_requirements():
        return
    
    # 运行应用
    run_streamlit()


if __name__ == "__main__":
    main()
