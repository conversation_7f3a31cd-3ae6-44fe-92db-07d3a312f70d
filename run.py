#!/usr/bin/env python3
"""
KontextFlux API 客户端启动脚本
"""

import subprocess
import sys
import os
import webbrowser
import time


def run_server():
    """运行本地服务器"""
    print("正在启动本地服务器...")
    try:
        # 启动 HTTP 服务器
        process = subprocess.Popen([sys.executable, "-m", "http.server", "8000"])

        # 等待服务器启动
        time.sleep(2)

        # 打开浏览器
        print("正在打开浏览器...")
        webbrowser.open("http://localhost:8000")

        print("✅ 应用已启动！")
        print("📱 浏览器地址: http://localhost:8000")
        print("⚠️  按 Ctrl+C 停止服务器")

        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n👋 正在停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")

    except Exception as e:
        print(f"❌ 启动失败: {e}")


def main():
    """主函数"""
    print("🎨 KontextFlux API 客户端")
    print("=" * 50)

    # 检查必要文件
    if not os.path.exists("index.html"):
        print("❌ 未找到 index.html 文件")
        return

    if not os.path.exists("workers.js"):
        print("❌ 未找到 workers.js 文件")
        return

    print("✅ 文件检查通过")

    # 运行服务器
    run_server()


if __name__ == "__main__":
    main()
