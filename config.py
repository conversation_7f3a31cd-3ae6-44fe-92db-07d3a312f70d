import streamlit as st
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器"""
    
    @staticmethod
    def load_config() -> Dict[str, Any]:
        """从 session state 加载配置"""
        if 'config' not in st.session_state:
            st.session_state.config = {}
        return st.session_state.config
    
    @staticmethod
    def save_config(config: Dict[str, Any]) -> None:
        """保存配置到 session state"""
        st.session_state.config = config
    
    @staticmethod
    def get_proxy_settings() -> Optional[str]:
        """获取代理设置"""
        return st.session_state.get('proxy_url', None)
    
    @staticmethod
    def set_proxy_settings(proxy_url: Optional[str]) -> None:
        """设置代理"""
        st.session_state.proxy_url = proxy_url
    
    @staticmethod
    def clear_config() -> None:
        """清除配置"""
        if 'config' in st.session_state:
            del st.session_state.config
        if 'proxy_url' in st.session_state:
            del st.session_state.proxy_url
