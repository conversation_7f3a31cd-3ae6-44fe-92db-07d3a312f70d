# KontextFlux API 客户端 - 带图形化界面
import json
import hashlib
import httpx
import streamlit as st
import asyncio
from io import BytesIO
from typing import List, Dict, Any, Optional
from PIL import Image

# 全局配置
proxies = {"http": None, "https": None}  # 代理设置

class KontextFluxEncryptor:
    """KontextFlux 加密器类"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

    def get_xtx_hash(self, payload: Dict[str, Any]) -> str:
        """生成 xtx 哈希值"""
        data_str = json.dumps(payload, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()

# 工具函数
def run_async(coro):
    """运行异步函数的辅助函数"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop.run_until_complete(coro)

def set_proxy(proxy_url: Optional[str]):
    """设置代理"""
    global proxies
    proxies["http"] = proxy_url
    proxies["https"] = proxy_url

# 原始 API 函数
async def getConfig():
    url = "https://api.kontextflux.com/client/common/getConfig"

    payload = {"token": None, "referrer": ""}

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "Origin": "https://kontextflux.com",
        "Referer": "https://kontextflux.com/",
    }

    async with httpx.AsyncClient(proxy=proxies["http"]) as client:
        response = await client.post(url, data=json.dumps(payload), headers=headers)
        response.raise_for_status()
        return response.json()["data"]


async def upload_file(config, image_bytes: bytes, filename: str = "image.png"):
    """Upload image file to kontextflux."""
    url = "https://api.kontextflux.com/client/resource/uploadFile"
    files = [("file", (filename, BytesIO(image_bytes), "null"))]
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Authorization": config["token"],
        "xtx": KontextFluxEncryptor(config).get_xtx_hash({}),
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(url, files=files, headers=headers)
        response.raise_for_status()
        return response.json()["data"]


async def create_draw_task(
    config, prompt: str, keys: List[str] = [], size: str = "auto"
):
    url = "https://api.kontextflux.com/client/styleAI/draw"

    payload = {
        "keys": keys,
        "prompt": prompt,
        "size": size,
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "Authorization": config["token"],
        "xtx": KontextFluxEncryptor(config).get_xtx_hash(payload),
    }
    async with httpx.AsyncClient(proxy=proxies["http"]) as client:
        response = await client.post(url, data=json.dumps(payload), headers=headers)
        response.raise_for_status()
        return response.json()["data"]["id"]


# ========== 图形化界面部分 ==========

def main():
    """主应用函数"""
    st.set_page_config(
        page_title="KontextFlux API 客户端",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    st.title("🎨 KontextFlux API 客户端")
    st.markdown("---")

    # 左侧边栏
    with st.sidebar:
        st.header("⚙️ 控制面板")

        # 功能选择
        st.subheader("功能选择")
        function_choice = st.selectbox(
            "选择功能",
            ["配置管理", "文件上传", "AI 绘图"],
            index=0
        )

        st.markdown("---")

        # 代理设置
        st.subheader("🌐 代理设置")
        proxy_enabled = st.checkbox("启用代理")
        if proxy_enabled:
            proxy_url = st.text_input(
                "代理地址",
                placeholder="http://127.0.0.1:7890",
                help="格式: http://host:port"
            )
            set_proxy(proxy_url if proxy_url else None)
        else:
            set_proxy(None)

        st.markdown("---")

        # 配置状态
        st.subheader("📊 状态信息")
        if 'config' in st.session_state and st.session_state.config:
            st.success("✅ 配置已加载")
            config = st.session_state.config
            if 'token' in config:
                token_display = config['token'][:20] + "..." if len(config.get('token', '')) > 20 else config.get('token', 'N/A')
                st.info(f"Token: {token_display}")
        else:
            st.warning("⚠️ 未加载配置")

        # 清除配置按钮
        if st.button("🗑️ 清除配置", type="secondary"):
            if 'config' in st.session_state:
                del st.session_state.config
            st.rerun()

    # 主内容区域
    if function_choice == "配置管理":
        show_config_management()
    elif function_choice == "文件上传":
        show_file_upload()
    elif function_choice == "AI 绘图":
        show_ai_drawing()


def show_config_management():
    """显示配置管理界面"""
    st.header("⚙️ 配置管理")

    col1, col2 = st.columns([1, 1])

    with col1:
        st.subheader("获取配置")
        st.write("从 KontextFlux API 获取配置信息")

        if st.button("🔄 获取配置", type="primary"):
            try:
                with st.spinner("正在获取配置..."):
                    config = run_async(getConfig())
                    st.session_state.config = config
                    st.success("配置获取成功！")
                    st.rerun()
            except Exception as e:
                st.error(f"错误: {str(e)}")

    with col2:
        st.subheader("当前配置")
        if 'config' in st.session_state and st.session_state.config:
            st.json(st.session_state.config)
        else:
            st.info("暂无配置信息")


def show_file_upload():
    """显示文件上传界面"""
    st.header("📁 文件上传")

    if 'config' not in st.session_state or not st.session_state.config:
        st.warning("⚠️ 请先获取配置信息")
        return

    col1, col2 = st.columns([1, 1])

    with col1:
        st.subheader("上传图片")
        uploaded_file = st.file_uploader(
            "选择图片文件",
            type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
            help="支持 PNG, JPG, JPEG, GIF, BMP 格式"
        )

        if uploaded_file is not None:
            # 显示预览
            image = Image.open(uploaded_file)
            st.image(image, caption="图片预览", use_column_width=True)

            # 上传按钮
            if st.button("⬆️ 上传图片", type="primary"):
                try:
                    with st.spinner("正在上传图片..."):
                        # 将图片转换为字节
                        img_bytes = BytesIO()
                        image.save(img_bytes, format='PNG')
                        img_bytes.seek(0)
                        image_bytes = img_bytes.getvalue()

                        result = run_async(upload_file(st.session_state.config, image_bytes, uploaded_file.name))
                        st.session_state.upload_result = result
                        st.success("图片上传成功！")
                except Exception as e:
                    st.error(f"错误: {str(e)}")

    with col2:
        st.subheader("上传结果")
        if 'upload_result' in st.session_state:
            st.json(st.session_state.upload_result)
        else:
            st.info("暂无上传结果")