async def getConfig():
    url = "https://api.kontextflux.com/client/common/getConfig"

    payload = {"token": None, "referrer": ""}

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "Origin": "https://kontextflux.com",
        "Referer": "https://kontextflux.com/",
    }

    async with httpx.AsyncClient(proxy=proxies["http"]) as client:
        response = await client.post(url, data=json.dumps(payload), headers=headers)
        response.raise_for_status()
        return response.json()["data"]


async def upload_file(config, image_bytes: bytes, filename: str = "image.png"):
    """Upload image file to kontextflux."""
    url = "https://api.kontextflux.com/client/resource/uploadFile"
    files = [("file", (filename, BytesIO(image_bytes), "null"))]
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Authorization": config["token"],
        "xtx": KontextFluxEncryptor(config).get_xtx_hash({}),
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(url, files=files, headers=headers)
        response.raise_for_status()
        return response.json()["data"]


async def create_draw_task(
    config, prompt: str, keys: List[str] = [], size: str = "auto"
):
    url = "https://api.kontextflux.com/client/styleAI/draw"

    payload = {
        "keys": keys,
        "prompt": prompt,
        "size": size,
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "Authorization": config["token"],
        "xtx": KontextFluxEncryptor(config).get_xtx_hash(payload),
    }
    async with httpx.AsyncClient(proxy=proxies["http"]) as client:
        response = await client.post(url, data=json.dumps(payload), headers=headers)
        response.raise_for_status()
        return response.json()["data"]["id"]