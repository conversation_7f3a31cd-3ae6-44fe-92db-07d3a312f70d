// KontextFlux API 客户端 - 带图形化界面
// 全局配置
let config = null;
let proxyUrl = null;

// 加密器类
class KontextFluxEncryptor {
    constructor(config) {
        this.config = config;
    }

    async getXtxHash(payload) {
        // 生成 xtx 哈希值 (使用简化的哈希算法)
        const dataStr = JSON.stringify(payload, Object.keys(payload).sort());

        // 简单的哈希函数 (实际项目中应该使用真正的 MD5)
        let hash = 0;
        for (let i = 0; i < dataStr.length; i++) {
            const char = dataStr.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }

        // 转换为16进制字符串
        return Math.abs(hash).toString(16).padStart(8, '0') +
               Date.now().toString(16).slice(-8);
    }
}

// 工具函数
function setProxy(url) {
    proxyUrl = url;
}

function getHeaders(extraHeaders = {}) {
    return {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        ...extraHeaders
    };
}

// API 函数
async function getConfig() {
    const url = "https://api.kontextflux.com/client/common/getConfig";
    const payload = {"token": null, "referrer": ""};

    const headers = getHeaders({
        "Content-Type": "application/json",
        "Origin": "https://kontextflux.com",
        "Referer": "https://kontextflux.com/"
    });

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error('获取配置失败:', error);
        throw error;
    }
}

async function uploadFile(config, imageFile, filename = "image.png") {
    const url = "https://api.kontextflux.com/client/resource/uploadFile";
    const formData = new FormData();
    formData.append('file', imageFile, filename);

    const encryptor = new KontextFluxEncryptor(config);
    const xtxHash = await encryptor.getXtxHash({});

    const headers = getHeaders({
        "Authorization": config.token,
        "xtx": xtxHash
    });

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error('文件上传失败:', error);
        throw error;
    }
}

async function createDrawTask(config, prompt, keys = [], size = "auto") {
    const url = "https://api.kontextflux.com/client/styleAI/draw";
    const payload = {
        keys: keys,
        prompt: prompt,
        size: size
    };

    const encryptor = new KontextFluxEncryptor(config);
    const xtxHash = await encryptor.getXtxHash(payload);

    const headers = getHeaders({
        "Content-Type": "application/json",
        "Authorization": config.token,
        "xtx": xtxHash
    });

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.data.id;
    } catch (error) {
        console.error('创建绘图任务失败:', error);
        throw error;
    }
}

// ========== 图形化界面部分 ==========

// DOM 操作函数
function createElement(tag, className = '', innerHTML = '') {
    const element = document.createElement(tag);
    if (className) element.className = className;
    if (innerHTML) element.innerHTML = innerHTML;
    return element;
}

function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}

function updateConfigDisplay() {
    const configDisplay = document.getElementById('config-display');
    if (config) {
        configDisplay.innerHTML = `
            <h4>当前配置</h4>
            <pre>${JSON.stringify(config, null, 2)}</pre>
        `;
        document.getElementById('config-status').innerHTML = '✅ 配置已加载';
        document.getElementById('config-status').className = 'status success';
    } else {
        configDisplay.innerHTML = '<p>暂无配置信息</p>';
        document.getElementById('config-status').innerHTML = '⚠️ 未加载配置';
        document.getElementById('config-status').className = 'status warning';
    }
}

// 事件处理函数
async function handleGetConfig() {
    const button = document.getElementById('get-config-btn');
    button.disabled = true;
    button.textContent = '获取中...';

    try {
        config = await getConfig();
        updateConfigDisplay();
        showMessage('配置获取成功！', 'success');
    } catch (error) {
        showMessage(`获取配置失败: ${error.message}`, 'error');
    } finally {
        button.disabled = false;
        button.textContent = '🔄 获取配置';
    }
}

async function handleFileUpload() {
    if (!config) {
        showMessage('请先获取配置信息', 'warning');
        return;
    }

    const fileInput = document.getElementById('file-input');
    const file = fileInput.files[0];

    if (!file) {
        showMessage('请选择文件', 'warning');
        return;
    }

    const button = document.getElementById('upload-btn');
    button.disabled = true;
    button.textContent = '上传中...';

    try {
        const result = await uploadFile(config, file, file.name);
        document.getElementById('upload-result').innerHTML = `
            <h4>上传结果</h4>
            <pre>${JSON.stringify(result, null, 2)}</pre>
        `;
        showMessage('文件上传成功！', 'success');
    } catch (error) {
        showMessage(`文件上传失败: ${error.message}`, 'error');
    } finally {
        button.disabled = false;
        button.textContent = '⬆️ 上传图片';
    }
}

async function handleDrawTask() {
    if (!config) {
        showMessage('请先获取配置信息', 'warning');
        return;
    }

    const prompt = document.getElementById('prompt-input').value.trim();
    if (!prompt) {
        showMessage('请输入绘图提示词', 'warning');
        return;
    }

    const keysInput = document.getElementById('keys-input').value.trim();
    const keys = keysInput ? keysInput.split(',').map(k => k.trim()).filter(k => k) : [];
    const size = document.getElementById('size-select').value;

    const button = document.getElementById('draw-btn');
    button.disabled = true;
    button.textContent = '生成中...';

    try {
        const taskId = await createDrawTask(config, prompt, keys, size);
        document.getElementById('draw-result').innerHTML = `
            <h4>生成结果</h4>
            <p>任务ID: ${taskId}</p>
            <p>请等待图片生成完成...</p>
        `;
        showMessage(`绘图任务创建成功！任务ID: ${taskId}`, 'success');
    } catch (error) {
        showMessage(`创建绘图任务失败: ${error.message}`, 'error');
    } finally {
        button.disabled = false;
        button.textContent = '🎨 生成图片';
    }
}

function handleProxyToggle() {
    const checkbox = document.getElementById('proxy-checkbox');
    const input = document.getElementById('proxy-input');

    if (checkbox.checked) {
        input.style.display = 'block';
        input.focus();
    } else {
        input.style.display = 'none';
        setProxy(null);
    }
}

function handleProxyChange() {
    const input = document.getElementById('proxy-input');
    setProxy(input.value.trim() || null);
}

function clearConfig() {
    config = null;
    updateConfigDisplay();
    document.getElementById('upload-result').innerHTML = '';
    document.getElementById('draw-result').innerHTML = '';
    showMessage('配置已清除', 'info');
}

function switchTab(tabName) {
    // 隐藏所有标签页内容
    const contents = document.querySelectorAll('.tab-content');
    contents.forEach(content => content.style.display = 'none');

    // 移除所有标签页的活动状态
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => tab.classList.remove('active'));

    // 显示选中的标签页内容
    document.getElementById(`${tabName}-content`).style.display = 'block';

    // 激活选中的标签页
    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
}

// 文件预览功能
function handleFileSelect() {
    const fileInput = document.getElementById('file-input');
    const preview = document.getElementById('file-preview');
    const file = fileInput.files[0];

    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `
                <h4>图片预览</h4>
                <img src="${e.target.result}" alt="预览图片" style="max-width: 100%; max-height: 200px;">
                <p>文件名: ${file.name}</p>
                <p>大小: ${(file.size / 1024).toFixed(2)} KB</p>
            `;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
}

// ========== HTML 界面生成 ==========

function createInterface() {
    // 创建完整的 HTML 界面
    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 KontextFlux API 客户端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
        }

        .container {
            display: flex;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h2 {
            color: #3498db;
            margin-bottom: 20px;
            text-align: center;
        }

        .sidebar-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .sidebar-section h3 {
            color: #ecf0f1;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .tab {
            display: block;
            width: 100%;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: #34495e;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }

        .tab:hover {
            background: #3498db;
            transform: translateX(5px);
        }

        .tab.active {
            background: #3498db;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
        }

        .proxy-section {
            margin-top: 15px;
        }

        .proxy-section input[type="checkbox"] {
            margin-right: 8px;
        }

        .proxy-section input[type="text"] {
            width: 100%;
            padding: 8px;
            margin-top: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            display: none;
        }

        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .clear-btn {
            width: 100%;
            padding: 10px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
        }

        .clear-btn:hover {
            background: #c0392b;
        }

        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            padding: 12px 24px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-box pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }

        .message {
            padding: 12px 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: none;
            font-weight: bold;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        #file-preview img {
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <h2>⚙️ 控制面板</h2>

            <!-- 功能选择 -->
            <div class="sidebar-section">
                <h3>功能选择</h3>
                <button class="tab active" onclick="switchTab('config')">配置管理</button>
                <button class="tab" onclick="switchTab('upload')">文件上传</button>
                <button class="tab" onclick="switchTab('draw')">AI 绘图</button>
            </div>

            <!-- 代理设置 -->
            <div class="sidebar-section">
                <h3>🌐 代理设置</h3>
                <div class="proxy-section">
                    <label>
                        <input type="checkbox" id="proxy-checkbox" onchange="handleProxyToggle()">
                        启用代理
                    </label>
                    <input type="text" id="proxy-input" placeholder="http://127.0.0.1:7890"
                           onchange="handleProxyChange()" style="display: none;">
                </div>
            </div>

            <!-- 状态信息 -->
            <div class="sidebar-section">
                <h3>📊 状态信息</h3>
                <div id="config-status" class="status warning">⚠️ 未加载配置</div>
                <button class="clear-btn" onclick="clearConfig()">🗑️ 清除配置</button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <h1>🎨 KontextFlux API 客户端</h1>
                <p>功能强大的 AI 绘图和文件管理工具</p>
            </div>

            <!-- 消息提示 -->
            <div id="message" class="message"></div>

            <!-- 配置管理标签页 -->
            <div id="config-content" class="tab-content active">
                <div class="grid">
                    <div class="card">
                        <h3>获取配置</h3>
                        <p>从 KontextFlux API 获取配置信息</p>
                        <button id="get-config-btn" class="btn" onclick="handleGetConfig()">🔄 获取配置</button>
                    </div>
                    <div class="card">
                        <div id="config-display">
                            <p>暂无配置信息</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件上传标签页 -->
            <div id="upload-content" class="tab-content">
                <div class="grid">
                    <div class="card">
                        <h3>上传图片</h3>
                        <div class="form-group">
                            <label for="file-input">选择图片文件</label>
                            <input type="file" id="file-input" accept="image/*" onchange="handleFileSelect()">
                        </div>
                        <div id="file-preview"></div>
                        <button id="upload-btn" class="btn btn-success" onclick="handleFileUpload()">⬆️ 上传图片</button>
                    </div>
                    <div class="card">
                        <div id="upload-result">
                            <p>暂无上传结果</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI 绘图标签页 -->
            <div id="draw-content" class="tab-content">
                <div class="grid">
                    <div class="card">
                        <h3>绘图参数</h3>
                        <div class="form-group">
                            <label for="prompt-input">绘图提示词</label>
                            <textarea id="prompt-input" rows="4" placeholder="请输入您想要生成的图片描述..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="keys-input">关键词 (可选)</label>
                            <input type="text" id="keys-input" placeholder="用逗号分隔多个关键词">
                        </div>
                        <div class="form-group">
                            <label for="size-select">图片尺寸</label>
                            <select id="size-select">
                                <option value="auto">自动</option>
                                <option value="1024x1024">1024x1024</option>
                                <option value="1024x768">1024x768</option>
                                <option value="768x1024">768x1024</option>
                                <option value="512x512">512x512</option>
                            </select>
                        </div>
                        <button id="draw-btn" class="btn btn-success" onclick="handleDrawTask()">🎨 生成图片</button>
                    </div>
                    <div class="card">
                        <div id="draw-result">
                            <p>暂无生成任务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;

    // 将 HTML 写入到页面
    document.open();
    document.write(htmlContent);
    document.close();

    // 初始化界面
    setTimeout(() => {
        updateConfigDisplay();
        console.log('KontextFlux API 客户端已加载');
    }, 100);
}

// 自动初始化界面
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createInterface);
    } else {
        createInterface();
    }
}


# ========== 图形化界面部分 ==========

def main():
    """主应用函数"""
    st.set_page_config(
        page_title="KontextFlux API 客户端",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    st.title("🎨 KontextFlux API 客户端")
    st.markdown("---")

    # 左侧边栏
    with st.sidebar:
        st.header("⚙️ 控制面板")

        # 功能选择
        st.subheader("功能选择")
        function_choice = st.selectbox(
            "选择功能",
            ["配置管理", "文件上传", "AI 绘图"],
            index=0
        )

        st.markdown("---")

        # 代理设置
        st.subheader("🌐 代理设置")
        proxy_enabled = st.checkbox("启用代理")
        if proxy_enabled:
            proxy_url = st.text_input(
                "代理地址",
                placeholder="http://127.0.0.1:7890",
                help="格式: http://host:port"
            )
            set_proxy(proxy_url if proxy_url else None)
        else:
            set_proxy(None)

        st.markdown("---")

        # 配置状态
        st.subheader("📊 状态信息")
        if 'config' in st.session_state and st.session_state.config:
            st.success("✅ 配置已加载")
            config = st.session_state.config
            if 'token' in config:
                token_display = config['token'][:20] + "..." if len(config.get('token', '')) > 20 else config.get('token', 'N/A')
                st.info(f"Token: {token_display}")
        else:
            st.warning("⚠️ 未加载配置")

        # 清除配置按钮
        if st.button("🗑️ 清除配置", type="secondary"):
            if 'config' in st.session_state:
                del st.session_state.config
            st.rerun()

    # 主内容区域
    if function_choice == "配置管理":
        show_config_management()
    elif function_choice == "文件上传":
        show_file_upload()
    elif function_choice == "AI 绘图":
        show_ai_drawing()


def show_config_management():
    """显示配置管理界面"""
    st.header("⚙️ 配置管理")

    col1, col2 = st.columns([1, 1])

    with col1:
        st.subheader("获取配置")
        st.write("从 KontextFlux API 获取配置信息")

        if st.button("🔄 获取配置", type="primary"):
            try:
                with st.spinner("正在获取配置..."):
                    config = run_async(getConfig())
                    st.session_state.config = config
                    st.success("配置获取成功！")
                    st.rerun()
            except Exception as e:
                st.error(f"错误: {str(e)}")

    with col2:
        st.subheader("当前配置")
        if 'config' in st.session_state and st.session_state.config:
            st.json(st.session_state.config)
        else:
            st.info("暂无配置信息")


def show_file_upload():
    """显示文件上传界面"""
    st.header("📁 文件上传")

    if 'config' not in st.session_state or not st.session_state.config:
        st.warning("⚠️ 请先获取配置信息")
        return

    col1, col2 = st.columns([1, 1])

    with col1:
        st.subheader("上传图片")
        uploaded_file = st.file_uploader(
            "选择图片文件",
            type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
            help="支持 PNG, JPG, JPEG, GIF, BMP 格式"
        )

        if uploaded_file is not None:
            # 显示预览
            image = Image.open(uploaded_file)
            st.image(image, caption="图片预览", use_column_width=True)

            # 上传按钮
            if st.button("⬆️ 上传图片", type="primary"):
                try:
                    with st.spinner("正在上传图片..."):
                        # 将图片转换为字节
                        img_bytes = BytesIO()
                        image.save(img_bytes, format='PNG')
                        img_bytes.seek(0)
                        image_bytes = img_bytes.getvalue()

                        result = run_async(upload_file(st.session_state.config, image_bytes, uploaded_file.name))
                        st.session_state.upload_result = result
                        st.success("图片上传成功！")
                except Exception as e:
                    st.error(f"错误: {str(e)}")

    with col2:
        st.subheader("上传结果")
        if 'upload_result' in st.session_state:
            st.json(st.session_state.upload_result)
        else:
            st.info("暂无上传结果")