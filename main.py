import streamlit as st
from PIL import Image
import asyncio
from kontextflux_api import KontextFluxAPI
from config import ConfigManager
from utils import run_async, image_to_bytes, display_error, display_success, display_info, display_warning


def main():
    """主应用函数"""
    st.set_page_config(
        page_title="KontextFlux API 客户端",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    st.title("🎨 KontextFlux API 客户端")
    st.markdown("---")
    
    # 左侧边栏
    with st.sidebar:
        st.header("⚙️ 控制面板")
        
        # 功能选择
        st.subheader("功能选择")
        function_choice = st.selectbox(
            "选择功能",
            ["配置管理", "文件上传", "AI 绘图"],
            index=0
        )
        
        st.markdown("---")
        
        # 代理设置
        st.subheader("🌐 代理设置")
        proxy_enabled = st.checkbox("启用代理")
        proxy_url = ""
        if proxy_enabled:
            proxy_url = st.text_input(
                "代理地址",
                placeholder="http://127.0.0.1:7890",
                help="格式: http://host:port"
            )
        
        ConfigManager.set_proxy_settings(proxy_url if proxy_enabled and proxy_url else None)
        
        st.markdown("---")
        
        # 配置状态
        st.subheader("📊 状态信息")
        config = ConfigManager.load_config()
        if config:
            st.success("✅ 配置已加载")
            if 'token' in config:
                st.info(f"Token: {config['token'][:20]}..." if len(config.get('token', '')) > 20 else f"Token: {config.get('token', 'N/A')}")
        else:
            st.warning("⚠️ 未加载配置")
        
        # 清除配置按钮
        if st.button("🗑️ 清除配置", type="secondary"):
            ConfigManager.clear_config()
            st.rerun()
    
    # 主内容区域
    if function_choice == "配置管理":
        show_config_management()
    elif function_choice == "文件上传":
        show_file_upload()
    elif function_choice == "AI 绘图":
        show_ai_drawing()


def show_config_management():
    """显示配置管理界面"""
    st.header("⚙️ 配置管理")
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("获取配置")
        st.write("从 KontextFlux API 获取配置信息")
        
        if st.button("🔄 获取配置", type="primary"):
            try:
                with st.spinner("正在获取配置..."):
                    api = KontextFluxAPI(proxy=ConfigManager.get_proxy_settings())
                    config = run_async(api.get_config())
                    ConfigManager.save_config(config)
                    display_success("配置获取成功！")
                    st.rerun()
            except Exception as e:
                display_error(e)
    
    with col2:
        st.subheader("当前配置")
        config = ConfigManager.load_config()
        if config:
            st.json(config)
        else:
            st.info("暂无配置信息")


def show_file_upload():
    """显示文件上传界面"""
    st.header("📁 文件上传")
    
    config = ConfigManager.load_config()
    if not config:
        st.warning("⚠️ 请先获取配置信息")
        return
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("上传图片")
        uploaded_file = st.file_uploader(
            "选择图片文件",
            type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
            help="支持 PNG, JPG, JPEG, GIF, BMP 格式"
        )
        
        if uploaded_file is not None:
            # 显示预览
            image = Image.open(uploaded_file)
            st.image(image, caption="图片预览", use_column_width=True)
            
            # 上传按钮
            if st.button("⬆️ 上传图片", type="primary"):
                try:
                    with st.spinner("正在上传图片..."):
                        image_bytes = image_to_bytes(image)
                        api = KontextFluxAPI(proxy=ConfigManager.get_proxy_settings())
                        result = run_async(api.upload_file(config, image_bytes, uploaded_file.name))
                        st.session_state.upload_result = result
                        display_success("图片上传成功！")
                except Exception as e:
                    display_error(e)
    
    with col2:
        st.subheader("上传结果")
        if 'upload_result' in st.session_state:
            st.json(st.session_state.upload_result)
        else:
            st.info("暂无上传结果")


def show_ai_drawing():
    """显示 AI 绘图界面"""
    st.header("🎨 AI 绘图")
    
    config = ConfigManager.load_config()
    if not config:
        st.warning("⚠️ 请先获取配置信息")
        return
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("绘图参数")
        
        # 提示词输入
        prompt = st.text_area(
            "绘图提示词",
            placeholder="请输入您想要生成的图片描述...",
            height=100,
            help="详细描述您想要生成的图片内容"
        )
        
        # 关键词输入
        keys_input = st.text_input(
            "关键词 (可选)",
            placeholder="用逗号分隔多个关键词",
            help="例如: 风景, 油画, 高清"
        )
        
        # 尺寸选择
        size = st.selectbox(
            "图片尺寸",
            ["auto", "1024x1024", "1024x768", "768x1024", "512x512"],
            help="选择生成图片的尺寸"
        )
        
        # 生成按钮
        if st.button("🎨 生成图片", type="primary", disabled=not prompt.strip()):
            try:
                with st.spinner("正在生成图片..."):
                    keys = [k.strip() for k in keys_input.split(",") if k.strip()] if keys_input else []
                    api = KontextFluxAPI(proxy=ConfigManager.get_proxy_settings())
                    task_id = run_async(api.create_draw_task(config, prompt, keys, size))
                    st.session_state.draw_task_id = task_id
                    display_success(f"绘图任务创建成功！任务ID: {task_id}")
            except Exception as e:
                display_error(e)
    
    with col2:
        st.subheader("生成结果")
        if 'draw_task_id' in st.session_state:
            st.info(f"任务ID: {st.session_state.draw_task_id}")
            st.write("请等待图片生成完成...")
            # 这里可以添加轮询检查任务状态的逻辑
        else:
            st.info("暂无生成任务")


if __name__ == "__main__":
    main()
