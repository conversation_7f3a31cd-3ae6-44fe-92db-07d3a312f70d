<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 KontextFlux API 客户端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
        }

        .container {
            display: flex;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h2 {
            color: #3498db;
            margin-bottom: 20px;
            text-align: center;
        }

        .sidebar-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .sidebar-section h3 {
            color: #ecf0f1;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .tab {
            display: block;
            width: 100%;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: #34495e;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }

        .tab:hover {
            background: #3498db;
            transform: translateX(5px);
        }

        .tab.active {
            background: #3498db;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
        }

        .proxy-section {
            margin-top: 15px;
        }

        .proxy-section input[type="checkbox"] {
            margin-right: 8px;
        }

        .proxy-section input[type="text"] {
            width: 100%;
            padding: 8px;
            margin-top: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            display: none;
        }

        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .clear-btn {
            width: 100%;
            padding: 10px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
        }

        .clear-btn:hover {
            background: #c0392b;
        }

        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            padding: 12px 24px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-box pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }

        .message {
            padding: 12px 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: none;
            font-weight: bold;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        #file-preview img {
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <h2>⚙️ 控制面板</h2>
            
            <!-- 功能选择 -->
            <div class="sidebar-section">
                <h3>功能选择</h3>
                <button class="tab active" onclick="switchTab('config')">配置管理</button>
                <button class="tab" onclick="switchTab('upload')">文件上传</button>
                <button class="tab" onclick="switchTab('draw')">AI 绘图</button>
            </div>
            
            <!-- 代理设置 -->
            <div class="sidebar-section">
                <h3>🌐 代理设置</h3>
                <div class="proxy-section">
                    <label>
                        <input type="checkbox" id="proxy-checkbox" onchange="handleProxyToggle()">
                        启用代理
                    </label>
                    <input type="text" id="proxy-input" placeholder="http://127.0.0.1:7890" 
                           onchange="handleProxyChange()" style="display: none;">
                </div>
            </div>
            
            <!-- 状态信息 -->
            <div class="sidebar-section">
                <h3>📊 状态信息</h3>
                <div id="config-status" class="status warning">⚠️ 未加载配置</div>
                <button class="clear-btn" onclick="clearConfig()">🗑️ 清除配置</button>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <h1>🎨 KontextFlux API 客户端</h1>
                <p>功能强大的 AI 绘图和文件管理工具</p>
            </div>
            
            <!-- 消息提示 -->
            <div id="message" class="message"></div>
            
            <!-- 配置管理标签页 -->
            <div id="config-content" class="tab-content active">
                <div class="grid">
                    <div class="card">
                        <h3>获取配置</h3>
                        <p>从 KontextFlux API 获取配置信息</p>
                        <button id="get-config-btn" class="btn" onclick="handleGetConfig()">🔄 获取配置</button>
                    </div>
                    <div class="card">
                        <div id="config-display">
                            <p>暂无配置信息</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 文件上传标签页 -->
            <div id="upload-content" class="tab-content">
                <div class="grid">
                    <div class="card">
                        <h3>上传图片</h3>
                        <div class="form-group">
                            <label for="file-input">选择图片文件</label>
                            <input type="file" id="file-input" accept="image/*" onchange="handleFileSelect()">
                        </div>
                        <div id="file-preview"></div>
                        <button id="upload-btn" class="btn btn-success" onclick="handleFileUpload()">⬆️ 上传图片</button>
                    </div>
                    <div class="card">
                        <div id="upload-result">
                            <p>暂无上传结果</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI 绘图标签页 -->
            <div id="draw-content" class="tab-content">
                <div class="grid">
                    <div class="card">
                        <h3>绘图参数</h3>
                        <div class="form-group">
                            <label for="prompt-input">绘图提示词</label>
                            <textarea id="prompt-input" rows="4" placeholder="请输入您想要生成的图片描述..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="keys-input">关键词 (可选)</label>
                            <input type="text" id="keys-input" placeholder="用逗号分隔多个关键词">
                        </div>
                        <div class="form-group">
                            <label for="size-select">图片尺寸</label>
                            <select id="size-select">
                                <option value="auto">自动</option>
                                <option value="1024x1024">1024x1024</option>
                                <option value="1024x768">1024x768</option>
                                <option value="768x1024">768x1024</option>
                                <option value="512x512">512x512</option>
                            </select>
                        </div>
                        <button id="draw-btn" class="btn btn-success" onclick="handleDrawTask()">🎨 生成图片</button>
                    </div>
                    <div class="card">
                        <div id="draw-result">
                            <p>暂无生成任务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="workers.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateConfigDisplay();
            console.log('KontextFlux API 客户端已加载');
        });
    </script>
</body>
</html>
