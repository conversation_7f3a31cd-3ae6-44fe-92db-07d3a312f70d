@echo off
echo 🎨 KontextFlux API 客户端
echo ================================
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Python，请先安装 Python
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "app.html" (
    echo ❌ 未找到 app.html 文件
    pause
    exit /b 1
)

if not exist "workers.js" (
    echo ❌ 未找到 workers.js 文件
    pause
    exit /b 1
)

echo ✅ 文件检查通过
echo.

REM 启动服务器
echo 正在启动本地服务器...
echo 📱 浏览器地址: http://localhost:8000
echo ⚠️  按 Ctrl+C 停止服务器
echo.

REM 延迟启动浏览器
timeout /t 2 /nobreak >nul
start http://localhost:8000/app.html

REM 启动 HTTP 服务器
python -m http.server 8000

pause
