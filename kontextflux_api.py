import json
import hashlib
import httpx
from io import BytesIO
from typing import List, Dict, Any, Optional


class KontextFluxEncryptor:
    """KontextFlux 加密器类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    def get_xtx_hash(self, payload: Dict[str, Any]) -> str:
        """生成 xtx 哈希值"""
        # 这里是一个简化的实现，实际的加密逻辑可能更复杂
        data_str = json.dumps(payload, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()


class KontextFluxAPI:
    """KontextFlux API 客户端"""
    
    def __init__(self, proxy: Optional[str] = None):
        self.proxy = proxy
        self.proxies = {"http": proxy, "https": proxy} if proxy else None
        self.base_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept-Encoding": "gzip, deflate, br, zstd",
        }
    
    async def get_config(self) -> Dict[str, Any]:
        """获取配置信息"""
        url = "https://api.kontextflux.com/client/common/getConfig"
        
        payload = {"token": None, "referrer": ""}
        
        headers = {
            **self.base_headers,
            "Content-Type": "application/json",
            "Origin": "https://kontextflux.com",
            "Referer": "https://kontextflux.com/",
        }
        
        async with httpx.AsyncClient(proxies=self.proxies) as client:
            response = await client.post(url, data=json.dumps(payload), headers=headers)
            response.raise_for_status()
            return response.json()["data"]
    
    async def upload_file(self, config: Dict[str, Any], image_bytes: bytes, filename: str = "image.png") -> Dict[str, Any]:
        """上传图片文件到 kontextflux"""
        url = "https://api.kontextflux.com/client/resource/uploadFile"
        files = [("file", (filename, BytesIO(image_bytes), "image/png"))]
        
        headers = {
            **self.base_headers,
            "Authorization": config["token"],
            "xtx": KontextFluxEncryptor(config).get_xtx_hash({}),
        }
        
        async with httpx.AsyncClient(proxies=self.proxies) as client:
            response = await client.post(url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()["data"]
    
    async def create_draw_task(self, config: Dict[str, Any], prompt: str, keys: List[str] = None, size: str = "auto") -> str:
        """创建绘图任务"""
        if keys is None:
            keys = []
            
        url = "https://api.kontextflux.com/client/styleAI/draw"
        
        payload = {
            "keys": keys,
            "prompt": prompt,
            "size": size,
        }
        
        headers = {
            **self.base_headers,
            "Content-Type": "application/json",
            "Authorization": config["token"],
            "xtx": KontextFluxEncryptor(config).get_xtx_hash(payload),
        }
        
        async with httpx.AsyncClient(proxies=self.proxies) as client:
            response = await client.post(url, data=json.dumps(payload), headers=headers)
            response.raise_for_status()
            return response.json()["data"]["id"]
