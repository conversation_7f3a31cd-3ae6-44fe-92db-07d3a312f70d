import streamlit as st
import asyncio
from PIL import Image
from io import BytesIO
from typing import Optional, Any


def run_async(coro):
    """运行异步函数的辅助函数"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(coro)


def image_to_bytes(image: Image.Image, format: str = "PNG") -> bytes:
    """将 PIL Image 转换为字节"""
    img_bytes = BytesIO()
    image.save(img_bytes, format=format)
    img_bytes.seek(0)
    return img_bytes.getvalue()


def display_error(error: Exception) -> None:
    """显示错误信息"""
    st.error(f"错误: {str(error)}")


def display_success(message: str) -> None:
    """显示成功信息"""
    st.success(message)


def display_info(message: str) -> None:
    """显示信息"""
    st.info(message)


def display_warning(message: str) -> None:
    """显示警告"""
    st.warning(message)
