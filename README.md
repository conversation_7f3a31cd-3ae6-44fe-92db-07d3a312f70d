# 🎨 KontextFlux API 客户端

一个基于 Streamlit 的 KontextFlux API 图形化客户端，提供直观的左侧边栏界面。

## ✨ 功能特性

- 🖥️ **图形化界面**: 基于 Streamlit 的现代化 Web 界面
- 📱 **左侧边栏**: 直观的功能选择和参数设置
- ⚙️ **配置管理**: 自动获取和管理 API 配置
- 📁 **文件上传**: 支持多种图片格式上传
- 🎨 **AI 绘图**: 创建 AI 绘图任务
- 🌐 **代理支持**: 支持 HTTP 代理设置
- 💾 **状态管理**: 自动保存会话状态

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

```bash
python run.py
```

### 方法二：手动安装和运行

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **运行应用**
```bash
streamlit run main.py
```

## 📋 使用说明

### 1. 配置管理
- 点击左侧边栏的"配置管理"
- 点击"获取配置"按钮获取 API 配置
- 配置信息会自动保存在会话中

### 2. 文件上传
- 选择"文件上传"功能
- 上传支持的图片格式（PNG, JPG, JPEG, GIF, BMP）
- 预览图片后点击"上传图片"

### 3. AI 绘图
- 选择"AI 绘图"功能
- 输入详细的绘图提示词
- 可选择添加关键词和设置图片尺寸
- 点击"生成图片"创建绘图任务

### 4. 代理设置
- 在左侧边栏启用代理
- 输入代理地址（格式：http://host:port）
- 所有 API 请求将通过代理发送

## 📁 项目结构

```
kontextflux2apicf/
├── main.py              # 主应用文件
├── kontextflux_api.py   # API 客户端
├── config.py            # 配置管理
├── utils.py             # 工具函数
├── requirements.txt     # 依赖列表
├── run.py              # 启动脚本
├── workers_backup.py   # 原始代码备份
└── README.md           # 说明文档
```

## 🔧 技术栈

- **Streamlit**: Web 界面框架
- **httpx**: 异步 HTTP 客户端
- **Pillow**: 图片处理
- **asyncio**: 异步编程支持

## 📝 注意事项

1. 首次使用需要先获取配置信息
2. 确保网络连接正常，或正确设置代理
3. 上传的图片文件大小建议不超过 10MB
4. AI 绘图任务创建后需要等待处理完成

## 🐛 故障排除

### 常见问题

1. **依赖安装失败**
   - 确保 Python 版本 >= 3.7
   - 尝试升级 pip: `pip install --upgrade pip`

2. **网络连接问题**
   - 检查网络连接
   - 配置正确的代理设置

3. **配置获取失败**
   - 检查 API 服务是否正常
   - 确认代理设置正确

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
