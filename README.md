# 🎨 KontextFlux API 客户端

一个基于 HTML/JavaScript 的 KontextFlux API 图形化客户端，提供直观的左侧边栏界面。

## ✨ 功能特性

- 🖥️ **图形化界面**: 基于 HTML/CSS/JavaScript 的现代化 Web 界面
- 📱 **左侧边栏**: 直观的功能选择和参数设置
- ⚙️ **配置管理**: 自动获取和管理 API 配置
- 📁 **文件上传**: 支持多种图片格式上传
- 🎨 **AI 绘图**: 创建 AI 绘图任务
- 🌐 **代理支持**: 支持 HTTP 代理设置
- 💾 **响应式设计**: 支持桌面和移动设备

## 🚀 快速开始

### 方法一：直接打开 HTML 文件

1. **下载项目文件**
   - `index.html` - 主界面文件
   - `workers.js` - JavaScript 功能代码

2. **打开应用**
   - 直接双击 `index.html` 文件
   - 或者在浏览器中打开 `index.html`

### 方法二：使用本地服务器（推荐）

```bash
# 使用 Python 启动本地服务器
python -m http.server 8000

# 或使用 Node.js
npx http-server

# 然后在浏览器中访问 http://localhost:8000
```

## 📋 使用说明

### 1. 配置管理
- 点击左侧边栏的"配置管理"标签
- 点击"🔄 获取配置"按钮获取 API 配置
- 配置信息会显示在右侧面板中

### 2. 文件上传
- 选择"文件上传"标签
- 点击"选择图片文件"上传支持的图片格式
- 预览图片后点击"⬆️ 上传图片"
- 上传结果会显示在右侧面板中

### 3. AI 绘图
- 选择"AI 绘图"标签
- 输入详细的绘图提示词
- 可选择添加关键词和设置图片尺寸
- 点击"🎨 生成图片"创建绘图任务

### 4. 代理设置
- 在左侧边栏勾选"启用代理"
- 输入代理地址（格式：http://host:port）
- 所有 API 请求将通过代理发送

## 📁 项目结构

```
kontextflux2apicf/
├── index.html           # 主界面文件
├── workers.js           # JavaScript 功能代码
├── README.md           # 说明文档
└── 其他文件...          # 之前创建的 Python 文件（可选）
```

## 🔧 技术栈

- **HTML5**: 页面结构
- **CSS3**: 样式设计和响应式布局
- **JavaScript (ES6+)**: 功能逻辑和 API 调用
- **Fetch API**: HTTP 请求处理

## 📝 注意事项

1. 首次使用需要先获取配置信息
2. 确保网络连接正常，或正确设置代理
3. 上传的图片文件大小建议不超过 10MB
4. AI 绘图任务创建后需要等待处理完成

## 🐛 故障排除

### 常见问题

1. **依赖安装失败**
   - 确保 Python 版本 >= 3.7
   - 尝试升级 pip: `pip install --upgrade pip`

2. **网络连接问题**
   - 检查网络连接
   - 配置正确的代理设置

3. **配置获取失败**
   - 检查 API 服务是否正常
   - 确认代理设置正确

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
